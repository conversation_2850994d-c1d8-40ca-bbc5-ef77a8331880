{"collectionName": "components_shared_items", "info": {"displayName": "cart-item", "description": ""}, "options": {}, "attributes": {"product": {"type": "relation", "relation": "oneToOne", "target": "api::product.product"}, "quantity": {"type": "integer"}, "variation_id": {"type": "string"}, "variation_details": {"type": "json", "description": "Snapshot of variation details at time of order (size, color, sku, price_adjustment)"}}}
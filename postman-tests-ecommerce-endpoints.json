{"info": {"name": "EEF Express - E-commerce Endpoints", "description": "Complete test collection for cart, checkout, orders, and products endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:1337/api", "type": "string"}, {"key": "authToken", "value": "{{jwt_token}}", "type": "string"}, {"key": "productId", "value": "1", "type": "string"}, {"key": "orderId", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "item": [{"name": "🛍️ Products", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products?page=1&pageSize=10&sort=createdAt:desc", "host": ["{{baseUrl}}"], "path": ["products"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "sort", "value": "createdAt:desc"}]}}, "response": []}, {"name": "Get Products with Filters", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products?minPrice=10&maxPrice=100&category=1&search=phone&inStock=true&minRating=4", "host": ["{{baseUrl}}"], "path": ["products"], "query": [{"key": "minPrice", "value": "10"}, {"key": "maxPrice", "value": "100"}, {"key": "category", "value": "1"}, {"key": "search", "value": "phone"}, {"key": "inStock", "value": "true"}, {"key": "minRating", "value": "4"}]}}, "response": []}, {"name": "Get Product Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"]}}, "response": []}, {"name": "Get Related Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}/related?limit=4", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}", "related"], "query": [{"key": "limit", "value": "4"}]}}, "response": []}, {"name": "Get Top Rated Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/top-rated?limit=5", "host": ["{{baseUrl}}"], "path": ["products", "top-rated"], "query": [{"key": "limit", "value": "5"}]}}, "response": []}, {"name": "Get Newest Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/newest?limit=5", "host": ["{{baseUrl}}"], "path": ["products", "newest"], "query": [{"key": "limit", "value": "5"}]}}, "response": []}]}, {"name": "🛒 Cart", "item": [{"name": "Get My Cart", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cart/me", "host": ["{{baseUrl}}"], "path": ["cart", "me"]}}, "response": []}, {"name": "Add Item to Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"quantity\": 2,\n  \"variationId\": null\n}"}, "url": {"raw": "{{baseUrl}}/cart/items", "host": ["{{baseUrl}}"], "path": ["cart", "items"]}}, "response": []}, {"name": "Add Item with Variation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"quantity\": 1,\n  \"variationId\": \"{{variationId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/cart/items", "host": ["{{baseUrl}}"], "path": ["cart", "items"]}}, "response": []}, {"name": "Test Variation Stock Limit", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"quantity\": 999,\n  \"variationId\": \"{{variationId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/cart/items", "host": ["{{baseUrl}}"], "path": ["cart", "items"]}}, "response": []}, {"name": "Update Item Quantity", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 3\n}"}, "url": {"raw": "{{baseUrl}}/cart/items/{{productId}}", "host": ["{{baseUrl}}"], "path": ["cart", "items", "{{productId}}"]}}, "response": []}, {"name": "Remove Item from Cart", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/cart/items/{{productId}}", "host": ["{{baseUrl}}"], "path": ["cart", "items", "{{productId}}"]}}, "response": []}, {"name": "Get Cart Totals", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cart/totals", "host": ["{{baseUrl}}"], "path": ["cart", "totals"]}}, "response": []}, {"name": "Validate <PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cart/validate", "host": ["{{baseUrl}}"], "path": ["cart", "validate"]}}, "response": []}, {"name": "Get Delivery Options", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cart/delivery-options", "host": ["{{baseUrl}}"], "path": ["cart", "delivery-options"]}}, "response": []}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/cart/clear", "host": ["{{baseUrl}}"], "path": ["cart", "clear"]}}, "response": []}]}, {"name": "💳 Checkout", "item": [{"name": "Get Order Summary", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/checkout/summary", "host": ["{{baseUrl}}"], "path": ["checkout", "summary"]}}, "response": []}, {"name": "Get Payment Methods", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/checkout/payment-methods", "host": ["{{baseUrl}}"], "path": ["checkout", "payment-methods"]}}, "response": []}, {"name": "Create Payment Intent", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"amount\": 100.50\n}"}, "url": {"raw": "{{baseUrl}}/checkout/create-payment-intent", "host": ["{{baseUrl}}"], "path": ["checkout", "create-payment-intent"]}}, "response": []}, {"name": "Process Checkout - Card Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deliveryType\": \"Standard\",\n  \"paymentMethod\": \"card\",\n  \"paymentDetails\": {\n    \"cardNumber\": \"****************\",\n    \"expiryMonth\": \"12\",\n    \"expiryYear\": \"2025\",\n    \"cvc\": \"123\",\n    \"cardholderName\": \"<PERSON>\"\n  },\n  \"shippingAddress\": {\n    \"name\": \"<PERSON>\",\n    \"addressLine1\": \"123 Main Street\",\n    \"addressLine2\": \"Apt 4B\",\n    \"emirate\": \"Dubai\",\n    \"phoneNumber\": \"+971501234567\",\n    \"instructions\": \"Leave at door\"\n  },\n  \"scheduledDateTime\": null\n}"}, "url": {"raw": "{{baseUrl}}/checkout", "host": ["{{baseUrl}}"], "path": ["checkout"]}}, "response": []}, {"name": "Process Checkout - Cash on Delivery", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deliveryType\": \"Standard\",\n  \"paymentMethod\": \"cash_on_delivery\",\n  \"paymentDetails\": {},\n  \"shippingAddress\": {\n    \"name\": \"<PERSON>\",\n    \"addressLine1\": \"456 Oak Avenue\",\n    \"addressLine2\": \"\",\n    \"emirate\": \"Abu Dhabi\",\n    \"phoneNumber\": \"+971507654321\",\n    \"instructions\": \"Call before delivery\"\n  },\n  \"scheduledDateTime\": null\n}"}, "url": {"raw": "{{baseUrl}}/checkout", "host": ["{{baseUrl}}"], "path": ["checkout"]}}, "response": []}, {"name": "Pay for Existing Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"customerEmail\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/checkout/pay/{{orderId}}", "host": ["{{baseUrl}}"], "path": ["checkout", "pay", "{{orderId}}"]}}, "response": []}]}, {"name": "📦 Orders", "item": [{"name": "Create Order from Cart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"deliveryType\": \"Standard\",\n  \"paymentMethod\": \"cash_on_delivery\",\n  \"shippingAddress\": {\n    \"name\": \"<PERSON>\",\n    \"addressLine1\": \"123 Main Street\",\n    \"addressLine2\": \"Apt 4B\",\n    \"emirate\": \"Dubai\",\n    \"phoneNumber\": \"+971501234567\",\n    \"instructions\": \"Leave at door\"\n  },\n  \"scheduledDateTime\": null\n}"}, "url": {"raw": "{{baseUrl}}/orders", "host": ["{{baseUrl}}"], "path": ["orders"]}}, "response": []}, {"name": "Get My Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/me?page=1&pageSize=10&sort=createdAt:desc", "host": ["{{baseUrl}}"], "path": ["orders", "me"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "sort", "value": "createdAt:desc"}]}}, "response": []}, {"name": "Get Order Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/{{orderId}}", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}"]}}, "response": []}, {"name": "Test Order with Variation Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/{{orderId}}", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}"]}, "description": "Test that order details include complete variation information for admin viewing"}, "response": []}, {"name": "Cancel Order", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"reason\": \"Changed my mind\"\n}"}, "url": {"raw": "{{baseUrl}}/orders/{{orderId}}/cancel", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}", "cancel"]}}, "response": []}]}, {"name": "👑 Admin - Orders", "item": [{"name": "Get All Orders (Admin)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/admin/all?page=1&pageSize=25&paymentStatus=completed&deliveryType=Standard", "host": ["{{baseUrl}}"], "path": ["orders", "admin", "all"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "25"}, {"key": "paymentStatus", "value": "completed"}, {"key": "deliveryType", "value": "Standard"}]}}, "response": []}, {"name": "Get Order Statistics (Admin)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/admin/stats?dateFrom=2024-01-01&dateTo=2024-12-31", "host": ["{{baseUrl}}"], "path": ["orders", "admin", "stats"], "query": [{"key": "dateFrom", "value": "2024-01-01"}, {"key": "dateTo", "value": "2024-12-31"}]}}, "response": []}, {"name": "Update Order Status (Admin)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"In Transit\",\n  \"locationNote\": \"Package left Dubai warehouse\"\n}"}, "url": {"raw": "{{baseUrl}}/orders/{{orderId}}/status", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}", "status"]}}, "response": []}, {"name": "Update Payment Status (Admin)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"completed\"\n}"}, "url": {"raw": "{{baseUrl}}/orders/{{orderId}}/payment-status", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}", "payment-status"]}}, "response": []}, {"name": "Search Orders (Admin)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/admin/search?search=john&userId=1&orderId=123", "host": ["{{baseUrl}}"], "path": ["orders", "admin", "search"], "query": [{"key": "search", "value": "john"}, {"key": "userId", "value": "1"}, {"key": "orderId", "value": "123"}]}}, "response": []}]}, {"name": "💰 Payments", "item": [{"name": "Get My Payments", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/payments/me?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["payments", "me"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}}, "response": []}, {"name": "Get Payment Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/payments/1", "host": ["{{baseUrl}}"], "path": ["payments", "1"]}}, "response": []}, {"name": "Update Payment Status (Admin)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"completed\"\n}"}, "url": {"raw": "{{baseUrl}}/payments/1/status", "host": ["{{baseUrl}}"], "path": ["payments", "1", "status"]}}, "response": []}]}, {"name": "🧪 Test Scenarios", "item": [{"name": "Complete E-commerce Flow Test", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products", "host": ["{{baseUrl}}"], "path": ["products"]}, "description": "1. Get products\n2. Add to cart\n3. Validate cart\n4. Checkout\n5. Check order status"}, "response": []}, {"name": "Test Product with Reviews and Ratings", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["products", "{{productId}}"]}, "description": "Test that product details include reviews, ratings, and variations with individual stock"}, "response": []}, {"name": "Test Variation Individual Stock - Add Valid Quantity", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"quantity\": 1,\n  \"variationId\": \"{{variationId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/cart/items", "host": ["{{baseUrl}}"], "path": ["cart", "items"]}, "description": "Test adding product variation with valid quantity within stock limit"}, "response": []}, {"name": "Test Variation Stock Limit - Exceed Stock", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"quantity\": 999,\n  \"variationId\": \"{{variationId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/cart/items", "host": ["{{baseUrl}}"], "path": ["cart", "items"]}, "description": "Test that adding quantity exceeding variation stock returns proper error"}, "response": []}, {"name": "Test Multiple Variations Same Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"quantity\": 1,\n  \"variationId\": \"{{variationId2}}\"\n}"}, "url": {"raw": "{{baseUrl}}/cart/items", "host": ["{{baseUrl}}"], "path": ["cart", "items"]}, "description": "Test adding different variation of same product - should be separate cart items"}, "response": []}, {"name": "Test Cart Totals with Variations", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cart/totals", "host": ["{{baseUrl}}"], "path": ["cart", "totals"]}, "description": "Test that cart totals correctly calculate variation price adjustments"}, "response": []}, {"name": "Test Cart Validation with Variations", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/cart/validate", "host": ["{{baseUrl}}"], "path": ["cart", "validate"]}, "description": "Test cart validation checks individual variation stock limits"}, "response": []}, {"name": "Test Update Variation Quantity", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"quantity\": 2\n}"}, "url": {"raw": "{{baseUrl}}/cart/items/{{productId}}", "host": ["{{baseUrl}}"], "path": ["cart", "items", "{{productId}}"]}, "description": "Test updating quantity respects variation stock limits"}, "response": []}, {"name": "Test Product Without Variation ID Error", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"{{productId}}\",\n  \"quantity\": 1\n}"}, "url": {"raw": "{{baseUrl}}/cart/items", "host": ["{{baseUrl}}"], "path": ["cart", "items"]}, "description": "Test that products with variations require variationId"}, "response": []}, {"name": "Test Order Variation Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/{{orderId}}", "host": ["{{baseUrl}}"], "path": ["orders", "{{orderId}}"]}, "description": "Test that order items include complete variation details for easy admin viewing"}, "response": []}, {"name": "Test Admin Order List with Variations", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/orders/admin/all?page=1&pageSize=10", "host": ["{{baseUrl}}"], "path": ["orders", "admin", "all"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "10"}]}, "description": "Test that admin order list shows variation details without manual lookup"}, "response": []}]}]}
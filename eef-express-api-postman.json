{"info": {"_postman_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6", "name": "EEF Express API", "description": "A comprehensive collection for testing the EEF Express e-commerce API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication & User Management", "description": "Endpoints for authentication and user management", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"identifier\": \"{{email}}\",\n    \"password\": \"{{password}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/local", "host": ["{{baseUrl}}"], "path": ["api", "auth", "local"]}, "description": "Login to get JWT token for authenticated requests"}, "response": []}, {"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"testuser\",\n    \"email\": \"{{email}}\",\n    \"password\": \"{{password}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/local/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "local", "register"]}, "description": "Register a new user (basic information only)"}, "response": []}, {"name": "Get Current User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/users/me", "host": ["{{baseUrl}}"], "path": ["api", "users", "me"]}, "description": "Get the profile of the currently authenticated user"}, "response": []}, {"name": "Update Current User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"Updated\",\n    \"lastName\": \"Name\",\n    \"phoneNumber\": \"+9876543210\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/me", "host": ["{{baseUrl}}"], "path": ["api", "users", "me"]}, "description": "Update the profile of the currently authenticated user"}, "response": []}, {"name": "Create Address", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"address\": {\n        \"addressLine1\": \"123 Main St\",\n        \"addressLine2\": \"Apt 456\",\n        \"city\": \"New York\",\n        \"state\": \"NY\",\n        \"postalCode\": \"10001\",\n        \"country\": \"USA\",\n        \"isDefault\": true,\n        \"label\": \"Home\",\n        \"phoneNumber\": \"+1234567890\"\n    }\n}"}, "url": {"raw": "{{baseUrl}}/api/users/addresses", "host": ["{{baseUrl}}"], "path": ["api", "users", "addresses"]}, "description": "Add a new address to the user's profile"}, "response": []}, {"name": "Get All Addresses", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/users/addresses", "host": ["{{baseUrl}}"], "path": ["api", "users", "addresses"]}, "description": "Get all addresses for the current user"}, "response": []}, {"name": "Update Address", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"address\": {\n        \"addressLine1\": \"456 Broadway\",\n        \"city\": \"New York\",\n        \"isDefault\": true\n    }\n}"}, "url": {"raw": "{{baseUrl}}/api/users/addresses/{{addressId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "addresses", "{{addressId}}"]}, "description": "Update an existing address"}, "response": []}, {"name": "Delete Address", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/users/addresses/{{addressId}}", "host": ["{{baseUrl}}"], "path": ["api", "users", "addresses", "{{addressId}}"]}, "description": "Delete an address"}, "response": []}]}, {"name": "Products", "description": "Endpoints for product management", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products?page=1&pageSize=10&sort=price:asc", "host": ["{{baseUrl}}"], "path": ["api", "products"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "pageSize", "value": "10", "description": "Number of items per page"}, {"key": "sort", "value": "price:asc", "description": "Field and direction to sort by"}]}, "description": "Get a paginated list of products with basic sorting"}, "response": []}, {"name": "Get Products with Filters", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products?page=1&pageSize=10&sort=price:asc&search=shirt&minPrice=10&maxPrice=100&category=1&inStock=true&minRating=4", "host": ["{{baseUrl}}"], "path": ["api", "products"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "pageSize", "value": "10", "description": "Number of items per page"}, {"key": "sort", "value": "price:asc", "description": "Field and direction to sort by"}, {"key": "search", "value": "shirt", "description": "Search term for name or description"}, {"key": "minPrice", "value": "10", "description": "Minimum price filter"}, {"key": "maxPrice", "value": "100", "description": "Maximum price filter"}, {"key": "category", "value": "1", "description": "Category ID filter"}, {"key": "inStock", "value": "true", "description": "Filter for products in stock"}, {"key": "minRating", "value": "4", "description": "Minimum rating filter"}]}, "description": "Get a paginated list of products with advanced filtering options"}, "response": []}, {"name": "Get Product Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products/{{productId}}", "host": ["{{baseUrl}}"], "path": ["api", "products", "{{productId}}"]}, "description": "Get detailed information about a specific product"}, "response": []}, {"name": "Get Related Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products/{{productId}}/related?limit=4", "host": ["{{baseUrl}}"], "path": ["api", "products", "{{productId}}", "related"], "query": [{"key": "limit", "value": "4", "description": "Maximum number of related products to return"}]}, "description": "Get products related to a specific product, based on the same category"}, "response": []}, {"name": "Get Top-Rated Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products/top-rated?limit=5", "host": ["{{baseUrl}}"], "path": ["api", "products", "top-rated"], "query": [{"key": "limit", "value": "5", "description": "Maximum number of products to return"}]}, "description": "Get the top-rated products (products with ratings of 4 or higher)"}, "response": []}, {"name": "Get Newest Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/products/newest?limit=5", "host": ["{{baseUrl}}"], "path": ["api", "products", "newest"], "query": [{"key": "limit", "value": "5", "description": "Maximum number of products to return"}]}, "description": "Get the newest products (most recently added products that are in stock)"}, "response": []}]}, {"name": "<PERSON><PERSON>", "description": "Endpoints for cart management", "item": [{"name": "Get My Cart", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/cart/me", "host": ["{{baseUrl}}"], "path": ["api", "cart", "me"]}, "description": "Get the current user's cart"}, "response": []}, {"name": "Add Item to Cart", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"productId\": {{productId}},\n    \"quantity\": 2\n}"}, "url": {"raw": "{{baseUrl}}/api/cart/items", "host": ["{{baseUrl}}"], "path": ["api", "cart", "items"]}, "description": "Add an item to the cart"}, "response": []}, {"name": "Remove Item from Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/cart/items/{{productId}}", "host": ["{{baseUrl}}"], "path": ["api", "cart", "items", "{{productId}}"]}, "description": "Remove an item from the cart"}, "response": []}, {"name": "Update Item Quantity", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"quantity\": 3\n}"}, "url": {"raw": "{{baseUrl}}/api/cart/items/{{productId}}", "host": ["{{baseUrl}}"], "path": ["api", "cart", "items", "{{productId}}"]}, "description": "Update item quantity in the cart"}, "response": []}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/cart/clear", "host": ["{{baseUrl}}"], "path": ["api", "cart", "clear"]}, "description": "Clear all items from the cart"}, "response": []}, {"name": "Get Cart Totals", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/cart/totals", "host": ["{{baseUrl}}"], "path": ["api", "cart", "totals"]}, "description": "Get cart totals"}, "response": []}]}, {"name": "Checkout", "description": "Endpoints for checkout process", "item": [{"name": "Process Checkout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deliveryType\": \"Express\",\n    \"paymentMethod\": \"credit_card\",\n    \"shippingAddress\": {{shippingAddressId}},\n    \"scheduledDateTime\": \"2023-06-01T14:00:00Z\",\n    \"paymentDetails\": {\n        \"cardNumber\": \"****************\",\n        \"expiryDate\": \"12/25\",\n        \"cvv\": \"123\"\n    }\n}"}, "url": {"raw": "{{baseUrl}}/api/checkout", "host": ["{{baseUrl}}"], "path": ["api", "checkout"]}, "description": "Process checkout and create an order"}, "response": []}, {"name": "Validate Coupon", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/checkout/coupon/WELCOME10", "host": ["{{baseUrl}}"], "path": ["api", "checkout", "coupon", "WELCOME10"]}, "description": "Validate a coupon code"}, "response": []}, {"name": "Get Order Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/checkout/summary?couponCode=WELCOME10", "host": ["{{baseUrl}}"], "path": ["api", "checkout", "summary"], "query": [{"key": "couponCode", "value": "WELCOME10", "description": "Optional coupon code to apply"}]}, "description": "Get order summary with pricing details"}, "response": []}]}, {"name": "Orders", "description": "Endpoints for order management", "item": [{"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"deliveryType\": \"Express\",\n    \"paymentMethod\": \"credit_card\",\n    \"shippingAddress\": {{shippingAddressId}},\n    \"scheduledDateTime\": \"2023-06-01T14:00:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/orders", "host": ["{{baseUrl}}"], "path": ["api", "orders"]}, "description": "Create a new order"}, "response": []}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"Shipped\",\n    \"locationNote\": \"Package left the warehouse\"\n}"}, "url": {"raw": "{{baseUrl}}/api/orders/{{orderId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "orders", "{{orderId}}", "status"]}, "description": "Update order status (admin only)"}, "response": []}, {"name": "Get My Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/orders/me?page=1&pageSize=10&sort=createdAt:desc", "host": ["{{baseUrl}}"], "path": ["api", "orders", "me"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "pageSize", "value": "10", "description": "Number of items per page"}, {"key": "sort", "value": "createdAt:desc", "description": "Field and direction to sort by"}]}, "description": "Get user's order history"}, "response": []}, {"name": "Get Order Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/orders/{{orderId}}", "host": ["{{baseUrl}}"], "path": ["api", "orders", "{{orderId}}"]}, "description": "Get order details"}, "response": []}]}, {"name": "Pick-Drop", "description": "Endpoints for delivery service", "item": [{"name": "Create Pick-Drop Request", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"senderName\": \"<PERSON>\",\n    \"senderContact\": \"+1234567890\",\n    \"receiverName\": \"<PERSON>\",\n    \"receiverContact\": \"+0987654321\",\n    \"itemDescription\": \"Small package with documents\",\n    \"itemWeight\": 2.5,\n    \"preferredPickupTime\": \"2023-06-01T10:00:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/pick-drops", "host": ["{{baseUrl}}"], "path": ["api", "pick-drops"]}, "description": "Create a new pick-drop request"}, "response": []}, {"name": "Update Pick-Drop Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"In Transit\",\n    \"assignedRider\": \"Rider Name\"\n}"}, "url": {"raw": "{{baseUrl}}/api/pick-drops/{{pickDropId}}/status", "host": ["{{baseUrl}}"], "path": ["api", "pick-drops", "{{pickDropId}}", "status"]}, "description": "Update pick-drop status (admin only)"}, "response": []}, {"name": "Get My Pick-Drops", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/pick-drops/me?page=1&pageSize=10&sort=createdAt:desc", "host": ["{{baseUrl}}"], "path": ["api", "pick-drops", "me"], "query": [{"key": "page", "value": "1", "description": "Page number for pagination"}, {"key": "pageSize", "value": "10", "description": "Number of items per page"}, {"key": "sort", "value": "createdAt:desc", "description": "Field and direction to sort by"}]}, "description": "Get user's pick-drop history"}, "response": []}, {"name": "Calculate Price", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/pick-drops/calculate-price?weight=2.5", "host": ["{{baseUrl}}"], "path": ["api", "pick-drops", "calculate-price"], "query": [{"key": "weight", "value": "2.5", "description": "Weight of the item in kg"}]}, "description": "Calculate price based on weight"}, "response": []}, {"name": "Get Pick-Drop Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/pick-drops/{{pickDropId}}", "host": ["{{baseUrl}}"], "path": ["api", "pick-drops", "{{pickDropId}}"]}, "description": "Get pick-drop details"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:1337", "type": "string", "description": "Base URL for the API"}, {"key": "email", "value": "<EMAIL>", "type": "string", "description": "Email for authentication"}, {"key": "password", "value": "your-password", "type": "string", "description": "Password for authentication"}, {"key": "token", "value": "your-jwt-token", "type": "string", "description": "JWT token for authenticated requests"}, {"key": "productId", "value": "1", "type": "string", "description": "ID of a product for testing"}, {"key": "orderId", "value": "1", "type": "string", "description": "ID of an order for testing"}, {"key": "pickDropId", "value": "1", "type": "string", "description": "ID of a pick-drop request for testing"}, {"key": "shippingAddressId", "value": "1", "type": "string", "description": "ID of a shipping address for testing"}, {"key": "addressId", "value": "1", "type": "string", "description": "ID of a user address for testing"}]}
{"name": "eef-express-cms", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry"}, "dependencies": {"@strapi/plugin-cloud": "5.13.0", "@strapi/plugin-users-permissions": "5.13.0", "@strapi/provider-upload-cloudinary": "^5.13.1", "@strapi/strapi": "5.13.0", "better-sqlite3": "11.3.0", "pg": "^8.16.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "stripe": "^18.1.1", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "2ecdf81cce38adb4ccda5f2c70c67e876752740bd71051e2b840fd529fc9bce3"}}
{"kind": "collectionType", "collectionName": "wishlists", "info": {"singularName": "wishlist", "pluralName": "wishlists", "displayName": "wishlist"}, "options": {"draftAndPublish": false}, "attributes": {"users_permissions_user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "inversedBy": "wishlist"}, "item": {"type": "component", "repeatable": true, "component": "shared.items"}}}
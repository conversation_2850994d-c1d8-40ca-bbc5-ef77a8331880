{"kind": "collectionType", "collectionName": "orders", "info": {"singularName": "order", "pluralName": "orders", "displayName": "Order", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"users_permissions_user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "orders"}, "deliveryType": {"type": "enumeration", "enum": ["Standard", "Scheduled", "Next-Day", "Same-Day", "Express"]}, "deliveryFee": {"type": "decimal"}, "totalAmount": {"type": "decimal"}, "subTotal": {"type": "decimal"}, "scheduledDateTime": {"type": "datetime"}, "paymentMethod": {"type": "enumeration", "enum": ["card", "paypal", "apple_pay", "google_pay", "cash_on_delivery"]}, "paymentStatus": {"type": "enumeration", "enum": ["pending", "processing", "completed", "failed", "refunded"], "default": "pending"}, "payment": {"type": "relation", "relation": "oneToOne", "target": "api::payment.payment", "mappedBy": "order"}, "products": {"type": "component", "repeatable": true, "component": "shared.items"}, "orderStatus": {"type": "component", "repeatable": true, "component": "custom.status-log"}, "shippingAddress": {"type": "component", "repeatable": false, "component": "address.address"}, "stripeId": {"type": "string"}}}
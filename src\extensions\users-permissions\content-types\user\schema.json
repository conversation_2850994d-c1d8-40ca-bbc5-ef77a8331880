{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "wishlist": {"type": "relation", "relation": "oneToOne", "target": "api::wishlist.wishlist", "mappedBy": "users_permissions_user"}, "cart": {"type": "relation", "relation": "oneToOne", "target": "api::cart.cart", "mappedBy": "users_permissions_user"}, "reviews": {"type": "relation", "relation": "oneToMany", "target": "api::review.review", "mappedBy": "users_permissions_user"}, "notifications": {"type": "relation", "relation": "oneToMany", "target": "api::notification.notification", "mappedBy": "users_permissions_user"}, "pick_drops": {"type": "relation", "relation": "oneToMany", "target": "api::pick-drop.pick-drop", "mappedBy": "users_permissions_user"}, "orders": {"type": "relation", "relation": "oneToMany", "target": "api::order.order", "mappedBy": "users_permissions_user"}, "addresses": {"type": "component", "repeatable": true, "component": "address.address"}, "payments": {"type": "relation", "relation": "oneToMany", "target": "api::payment.payment", "mappedBy": "users_permissions_user"}}}